# 忽略前端文件夹
frontend/

# Git 相关
.git/
.gitignore

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/*.log
logs/*.zip

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 测试相关
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# 文档
docs/_build/
.readthedocs.yml

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 其他
*.log
*.pid
*.seed
*.pid.lock
.npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 概要设计文档
概要设计/

# README 和文档
README.md
*.md

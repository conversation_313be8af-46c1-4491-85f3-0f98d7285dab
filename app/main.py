"""
FastAPI应用入口
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse

from app.core.config import settings
from app.core.database import create_tables
from app.core.logging import get_logger
from app.api.v1 import departments, dormitories, residents, records, reports, auth
from starlette.responses import RedirectResponse

logger = get_logger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="宿舍入住管理系统API",
    debug=settings.debug
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
# 认证路由（不需要认证）
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["auth"]
)

app.include_router(
    departments.router,
    prefix="/api/v1/departments",
    tags=["departments"]
)

app.include_router(
    dormitories.router,
    prefix="/api/v1/dormitories",
    tags=["dormitories"]
)

app.include_router(
    residents.router,
    prefix="/api/v1/residents",
    tags=["residents"]
)

app.include_router(
    records.router,
    prefix="/api/v1/records",
    tags=["records"]
)

app.include_router(
    reports.router,
    prefix="/api/v1/reports",
    tags=["reports"]
)

# 静态文件服务（前端资源）

app.mount("/static", StaticFiles(directory="static"), name="static")

# 访问根路径时跳转到 login.html
@app.get("/")
async def redirect_to_login():
    return RedirectResponse(url="/static/login.html")



@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"启动 {settings.app_name} v{settings.app_version}")
    
    # 创建数据库表
    try:
        create_tables()
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {str(e)}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("应用正在关闭...")


@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回简单的HTML页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>宿舍入住管理系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 40px; }
            .api-link { display: inline-block; margin: 10px; padding: 10px 20px; 
                       background: #007bff; color: white; text-decoration: none; 
                       border-radius: 5px; }
            .api-link:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>宿舍入住管理系统</h1>
                <p>版本: """ + settings.app_version + """</p>
            </div>
            <div>
                <h2>API文档</h2>
                <a href="/docs" class="api-link">Swagger UI</a>
                <a href="/redoc" class="api-link">ReDoc</a>
            </div>
            <div>
                <h2>功能模块</h2>
                <ul>
                    <li>部门管理</li>
                    <li>宿舍管理</li>
                    <li>住户管理</li>
                    <li>入住记录管理</li>
                    <li>费用分摊报表</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )

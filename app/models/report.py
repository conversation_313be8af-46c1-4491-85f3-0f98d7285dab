"""
报表模型
"""
from sqlalchemy import Column, String, Integer, Date, Text, ForeignKey, CheckConstraint, JSON, Numeric
from sqlalchemy.orm import relationship
from .base import BaseModel


class MonthlyReport(BaseModel):
    """月度报告模型"""
    __tablename__ = "monthly_reports"

    year = Column(Integer, nullable=False, comment="年份")
    month = Column(Integer, nullable=False, comment="月份")
    report_date = Column(Date, nullable=False, comment="报告日期")
    department_summary = Column(JSON, nullable=False, comment="部门汇总信息")
    daily_details = Column(JSON, nullable=False, comment="日度明细信息")
    
    # 约束
    __table_args__ = (
        CheckConstraint('month BETWEEN 1 AND 12', name='chk_month_valid'),
        CheckConstraint('year >= 1900', name='chk_year_valid'),
    )
    
    # 关联关系
    daily_allocations = relationship("DailyAllocation", back_populates="monthly_report")
    
    def __repr__(self):
        return f"<MonthlyReport(id={self.id}, year={self.year}, month={self.month})>"


class DailyAllocation(BaseModel):
    """日度分摊模型"""
    __tablename__ = "daily_allocations"
    
    allocation_date = Column(Date, nullable=False, comment="分摊日期")
    dormitory_id = Column(String(50), ForeignKey("dormitories.id"), nullable=False, comment="宿舍ID")
    total_beds = Column(Integer, nullable=False, comment="总床位数")
    occupied_beds = Column(Integer, nullable=False, comment="已占用床位数")
    department_allocations = Column(JSON, nullable=False, comment="部门分摊详情")
    monthly_report_id = Column(String(50), ForeignKey("monthly_reports.id"), comment="月度报告ID")
    
    # 约束
    __table_args__ = (
        CheckConstraint('occupied_beds >= 0 AND occupied_beds <= total_beds',
                       name='chk_occupied_beds_valid'),
        CheckConstraint('total_beds > 0', name='chk_daily_allocation_total_beds_positive'),
    )
    
    # 关联关系
    dormitory = relationship("Dormitory", back_populates="daily_allocations")
    monthly_report = relationship("MonthlyReport", back_populates="daily_allocations")
    
    def __repr__(self):
        return f"<DailyAllocation(id={self.id}, date={self.allocation_date}, dormitory={self.dormitory_id})>"
